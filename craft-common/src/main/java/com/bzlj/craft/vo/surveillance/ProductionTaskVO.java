package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 任务列表模型
 * @date 2025-03-11 17:48
 */
@Data
public class ProductionTaskVO {

    @ComponentProperty(label = "任务id", display = false,order = 15, path = "taskId")
    private String taskId;

    @ComponentProperty(label = "任务编号", searchable = true, order = 1, display = false, path = "taskCode")
    private String taskCode;

    @ComponentProperty(label = "工序类型", searchable = true, order = 2, path = "process.processType.itemCode",dataSourceType = DataSourceType.DICT,dictCode = "PROCESS_TYPE")
    private String processType;

    @ComponentProperty(label = "工艺卡号", searchable = true, order = 3, path = "process.craft.craftCode")
    private String craftCode;

    @ComponentProperty(label = "设备编号", order = 4, path = "taskEquipments.equip.code")
    private String equipmentCode;

    @ComponentProperty(label = "设备名称", order = 5, path = "taskEquipments.equip.name")
    private String equipmentName;

    @ComponentProperty(label = "牌号", order = 6, path = "taskMaterials.material.brand")
    private String brand;

    @ComponentProperty(label = "投入物料编号", order = 7, path = "taskMaterials.material.materialCode",alias = "inputMaterialCode")
    private String inputMaterialCode;

    @ComponentProperty(label = "产出物料编号", order = 8, path = "taskMaterials.material.materialCode", alias = "outputMaterialCode")
    private String outputMaterialCode;

    @ComponentProperty(label = "计划开始时间", order = 9, path = "planStartTime", range = true)
    private LocalDateTime planStartTime;

    @ComponentProperty(label = "计划结束时间", order = 10, path = "planEndTime", range = true)
    private LocalDateTime planEndTime;

    @ComponentProperty(label = "实际开始时间", order = 11, path = "startTime", range = true)
    private LocalDateTime startTime;

    @ComponentProperty(label = "实际结束时间", order = 12, path = "endTime", range = true)
    private LocalDateTime endTime;

    /**
     * 计划生产重量
     */
    @ComponentProperty(label = "计划生产重量", order = 13, path = "planWeight", range = true)
    private BigDecimal planWeight;

    /**
     * 计划生产数量
     */
    @ComponentProperty(label = "计划生产数量", order = 14, path = "planQuantity", range = true)
    private Integer planQuantity;

    @ComponentProperty(label = "任务状态", path = "statusCode.itemCode",searchable = true,dataSourceType = DataSourceType.DICT, dictCode = "TASK_STATUS",order = 15)
    private String statusCode;

    @ComponentProperty(label = "工序评分", path = "score",order = 16)
    private BigDecimal score;

    @ComponentProperty(label = "所属分厂", order = 17, path = "plant.plantCode", searchable = true,initialRequest = true, dataSourceType = DataSourceType.API,
            dataSource = @ComponentProperty.DataSource(
                    apiUrl = "/craft/craft/findPlantList",
                    httpMethod = HttpMethod.GET,
                    mapping = @ComponentProperty.Mapping(
                            valueKey = "plantCode",
                            labelKey = "plantName"
                    )

            )
    )
    private String plantCode;
}
