# FormAttr Excel导入功能说明

## 功能概述

本功能实现了通过Excel文件批量导入FormAttr（表单属性）数据的能力，支持新增和更新操作，并提供详细的导入结果反馈。

## 主要特性

- ✅ Excel模板下载
- ✅ 批量数据导入
- ✅ 数据验证和错误提示
- ✅ 支持新增和更新操作
- ✅ 详细的导入结果统计
- ✅ 支持多种数据源类型（STATIC/DICT/API）

## API接口

### 1. 下载导入模板

**接口地址：** `GET /formAttr/downloadTemplate`

**功能：** 下载Excel格式的FormAttr导入模板

**响应：** Excel文件下载

### 2. 导入FormAttr数据

**接口地址：** `POST /formAttr/import`

**参数：**
- `file`: MultipartFile类型，Excel文件（.xlsx或.xls格式）

**响应示例：**
```json
{
  "code": 200,
  "msg": "请求成功",
  "data": {
    "totalCount": 10,
    "successCount": 8,
    "failureCount": 2,
    "updateCount": 3,
    "insertCount": 5,
    "allSuccess": false,
    "errors": [
      {
        "rowIndex": 5,
        "field": "组件ID",
        "message": "组件ID不能为空",
        "originalData": ""
      }
    ]
  },
  "success": true
}
```

## Excel模板格式

### 必填字段（标记*）
1. **组件ID*** - 组件的唯一标识
2. **属性名称*** - 属性的数据索引（如：material.name）
3. **属性标签*** - 显示标签（如：物料名称）
4. **字段类型*** - 数据类型，支持：
   - `string` - 字符串
   - `number` - 数字
   - `enum` - 枚举
   - `datetime` - 日期时间
   - `bool` - 布尔值
   - `obj` - 对象

### 可选字段
5. **是否支持搜索** - true/false
6. **属性排序** - 数字，排序顺序
7. **是否展示** - true/false
8. **是否范围** - true/false
9. **属性字典项** - 字典编码（当数据源类型为DICT时使用）
10. **属性格式** - 格式化字符串（如日期格式）
11. **属性别名** - 属性别名
12. **是否支持多选** - true/false
13. **初始请求** - true/false
14. **数据源类型** - 支持：
    - `STATIC` - 静态数据（默认）
    - `DICT` - 字典数据
    - `API` - API接口数据

### API数据源相关字段（当数据源类型为API时）
15. **API URL** - API接口地址
16. **HTTP方法** - GET/POST/PUT/DELETE
17. **API参数** - API请求参数
18. **值字段映射** - API返回数据中的值字段名
19. **标签字段映射** - API返回数据中的标签字段名

## 使用示例

### 示例1：基本字符串字段
```
组件ID: COMP001
属性名称: material.name
属性标签: 物料名称
字段类型: string
是否支持搜索: true
属性排序: 1
是否展示: true
数据源类型: STATIC
```

### 示例2：字典枚举字段
```
组件ID: COMP001
属性名称: status
属性标签: 状态
字段类型: enum
是否支持搜索: true
属性排序: 2
是否展示: true
属性字典项: STATUS_DICT
数据源类型: DICT
```

### 示例3：API数据源字段
```
组件ID: COMP001
属性名称: department
属性标签: 部门
字段类型: enum
是否支持搜索: false
属性排序: 3
是否展示: true
数据源类型: API
API URL: /api/departments
HTTP方法: GET
值字段映射: id
标签字段映射: name
初始请求: true
```

## 导入逻辑

1. **数据验证**：验证必填字段、数据类型、枚举值等
2. **重复检查**：根据`componentId + dataIndex`判断是否已存在
3. **操作类型**：
   - 如果记录已存在，则更新现有记录
   - 如果记录不存在，则新增记录
4. **结果统计**：返回详细的导入结果，包括成功/失败数量和错误信息

## 错误处理

常见错误类型：
- 必填字段为空
- 字段类型无效
- 数据源类型无效
- HTTP方法无效
- Excel文件格式错误

所有错误都会在导入结果中详细列出，包括行号、字段名和错误信息。

## 注意事项

1. Excel文件必须使用提供的模板格式
2. 第1行为标题行，第2行为说明行，从第3行开始为数据行
3. 空行会被自动跳过
4. 建议先下载模板，参考示例数据进行填写
5. 导入前请确保组件ID已存在于系统中
6. 大批量数据导入时，建议分批进行

## 技术实现

### 主要类文件
- `FormAttrController.java` - 控制器，提供API接口
- `IFormAttrImportService.java` - 服务接口
- `FormAttrImportServiceImpl.java` - 服务实现
- `FormAttrImportDTO.java` - 导入数据传输对象
- `ImportResultDTO.java` - 导入结果传输对象
- `ExcelTemplateUtil.java` - Excel模板生成工具

### 依赖
- Apache POI - Excel文件处理
- Spring Boot - Web框架
- MongoDB - 数据存储
- Lombok - 代码简化
