package com.bzlj.craft.component.service.impl;

import com.bzlj.base.component.annotation.ComponentProperty;
import com.bzlj.craft.component.def.FormAttrDTO;
import com.bzlj.craft.component.service.ComponentDealService;
import com.bzlj.craft.dto.SysDictItemDTO;
import com.bzlj.craft.enums.TypeEnum;
import com.bzlj.craft.mongo.entity.FormAttr;
import com.bzlj.craft.mongo.repository.FormAttrRepository;
import com.bzlj.craft.service.ISysDictItemService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reflections.ReflectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-07 11:14
 */
@Service
@Slf4j
public class ComponentDealServiceImpl implements ComponentDealService {

    @Autowired
    private ISysDictItemService sysDictItemService;

    @Autowired
    private FormAttrRepository formAttrRepository;

    public List<FormAttrDTO> modelConvertFormAttr(String modelClass,String componentId) {
        // 如果modelClass为空，则通过componentId从MongoDB查询FormAttr
        if (StringUtils.isEmpty(modelClass)) {
            return queryFormAttrFromMongo(componentId);
        }

        Class<?> clazz = null;
        try {
            clazz = Class.forName(modelClass);
        } catch (ClassNotFoundException e) {
            log.error("{}", e.getMessage(), e);
            return Lists.newArrayList();
        }
        Set<Field> allFields = ReflectionUtils.getAllFields(clazz);
        List<Field> fields = allFields.stream().filter(field -> field.isAnnotationPresent(ComponentProperty.class) && field.getAnnotation(ComponentProperty.class).enable()).toList();
        if (CollectionUtils.isEmpty(fields)) return Lists.newArrayList();
        return fields.stream().map(field -> {
            ComponentProperty componentProperty = field.getAnnotation(ComponentProperty.class);
            FormAttrDTO formAttrDTO = FormAttrDTO.builder()
                    .dataIndex(componentProperty.path())
                    .label(componentProperty.label())
                    .searchable(componentProperty.searchable())
                    .order(componentProperty.order())
                    .display(componentProperty.display())
                    .range(componentProperty.range())
                    .multiple(componentProperty.multiple())
                    .alias(StringUtils.isEmpty(componentProperty.alias()) ? null : componentProperty.alias())
                    .dataSourceType(componentProperty.dataSourceType())
                    .initialRequest(componentProperty.initialRequest())
                    .build();
            switch (componentProperty.dataSourceType()) {
                case DICT:
                    if (StringUtils.isNotEmpty(componentProperty.dictCode())) {
                        List<SysDictItemDTO> dictItems = sysDictItemService.findByDictCode(componentProperty.dictCode());
                        formAttrDTO.setDictItems(dictItems);
                        formAttrDTO.setType(TypeEnum.enums);
                    }
                    break;
                case API:
                    ComponentProperty.DataSource dataSource = componentProperty.dataSource();
                    formAttrDTO.setDataSource(buildDataSource(dataSource));
                    formAttrDTO.setType(TypeEnum.enums);
                    break;
                case STATIC:
                default:
                    String typeName = field.getType().getSimpleName();
                    formAttrDTO.setType(FormAttrDTO.formType(typeName));
                    if (TypeEnum.datetime.equals(formAttrDTO.getType())) {
                        formAttrDTO.setFormat(componentProperty.format());
                    }
                    break;
            }
            Class<?> subClass = componentProperty.subClass();
            if (subClass.isAssignableFrom(void.class)) {
                return formAttrDTO;
            }
            List<FormAttrDTO> formAttrDTOS = modelConvertFormAttr(subClass.getName(),componentId);
            formAttrDTO.setChildren(formAttrDTOS);
            return formAttrDTO;
        }).sorted(Comparator.comparing(FormAttrDTO::getOrder)).toList();
    }

    private FormAttrDTO.DataSource buildDataSource(ComponentProperty.DataSource dataSource) {
        if(Objects.nonNull(dataSource) && Objects.nonNull(dataSource.mapping())){
            return FormAttrDTO.DataSource.builder()
                    .api(FormAttrDTO.DataSource.Api.builder()
                            .url(dataSource.apiUrl())
                            .method(dataSource.httpMethod())
                            .params(dataSource.apiParams())
                            .build())
                    .mapping(FormAttrDTO.DataSource.Mapping.builder().labelKey(dataSource.mapping().labelKey())
                            .valueKey(dataSource.mapping().valueKey()).build())
                    .build();
        }
        return FormAttrDTO.DataSource.builder().build();
    }

    /**
     * 从MongoDB查询FormAttr并转换为FormAttrDTO
     * @param componentId 组件ID
     * @return FormAttrDTO列表
     */
    private List<FormAttrDTO> queryFormAttrFromMongo(String componentId) {
        if (StringUtils.isEmpty(componentId)) {
            return Lists.newArrayList();
        }

        List<FormAttr> formAttrs = formAttrRepository.findByComponentIdOrderByOrder(componentId);
        if (CollectionUtils.isEmpty(formAttrs)) {
            return Lists.newArrayList();
        }

        return formAttrs.stream().map(this::convertFormAttrToDTO)
                .sorted(Comparator.comparing(FormAttrDTO::getOrder))
                .toList();
    }

    /**
     * 将FormAttr实体转换为FormAttrDTO
     * @param formAttr FormAttr实体
     * @return FormAttrDTO
     */
    private FormAttrDTO convertFormAttrToDTO(FormAttr formAttr) {
        FormAttrDTO.FormAttrDTOBuilder builder = FormAttrDTO.builder()
                .dataIndex(formAttr.getDataIndex())
                .label(formAttr.getLabel())
                .type(formAttr.getType())
                .searchable(formAttr.isSearchable())
                .order(formAttr.getOrder())
                .display(formAttr.isDisplay())
                .range(formAttr.isRange())
                .format(formAttr.getFormat())
                .alias(formAttr.getAlias())
                .multiple(formAttr.getMultiple())
                .initialRequest(formAttr.getInitialRequest())
                .dataSourceType(formAttr.getDataSourceType());

        // 处理字典项
        if (StringUtils.isNotEmpty(formAttr.getDictCode())) {
            List<SysDictItemDTO> dictItems = sysDictItemService.findByDictCode(formAttr.getDictCode());
            builder.dictItems(dictItems);
        }

        // 处理数据源
        if (Objects.nonNull(formAttr.getDataSource())) {
            builder.dataSource(convertDataSource(formAttr.getDataSource()));
        }

        return builder.build();
    }

    /**
     * 转换数据源
     * @param dataSource MongoDB中的数据源
     * @return FormAttrDTO中的数据源
     */
    private FormAttrDTO.DataSource convertDataSource(FormAttr.DataSource dataSource) {
        if (Objects.isNull(dataSource)) {
            return FormAttrDTO.DataSource.builder().build();
        }

        FormAttrDTO.DataSource.DataSourceBuilder builder = FormAttrDTO.DataSource.builder();

        if (Objects.nonNull(dataSource.getApi())) {
            builder.api(FormAttrDTO.DataSource.Api.builder()
                    .url(dataSource.getApi().getUrl())
                    .method(dataSource.getApi().getMethod())
                    .params(dataSource.getApi().getParams())
                    .build());
        }

        if (Objects.nonNull(dataSource.getMapping())) {
            builder.mapping(FormAttrDTO.DataSource.Mapping.builder()
                    .labelKey(dataSource.getMapping().getLabelKey())
                    .valueKey(dataSource.getMapping().getValueKey())
                    .build());
        }

        return builder.build();
    }
}
