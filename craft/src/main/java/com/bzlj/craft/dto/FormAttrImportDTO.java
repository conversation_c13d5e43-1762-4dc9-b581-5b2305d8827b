package com.bzlj.craft.dto;

import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;
import com.bzlj.craft.enums.TypeEnum;
import lombok.Data;

/**
 * FormAttr Excel导入DTO
 * <AUTHOR>
 * @description: 用于Excel导入FormAttr数据的DTO
 * @date 2025-07-25
 */
@Data
public class FormAttrImportDTO {
    
    /**
     * 组件ID
     */
    private String componentId;
    
    /**
     * 属性名称
     */
    private String dataIndex;
    
    /**
     * 属性标签
     */
    private String label;
    
    /**
     * 字段类型（string/number/enum/datetime/bool/obj）
     */
    private String type;
    
    /**
     * 当前属性是否支持搜索（true/false）
     */
    private String searchable;
    
    /**
     * 属性排序
     */
    private Integer order;
    
    /**
     * 是否展示（true/false）
     */
    private String display;
    
    /**
     * 是否范围（true/false）
     */
    private String range;
    
    /**
     * 属性字典项
     */
    private String dictCode;
    
    /**
     * 属性格式
     */
    private String format;
    
    /**
     * 属性别名
     */
    private String alias;
    
    /**
     * 是否支持多选（true/false）
     */
    private String multiple;
    
    /**
     * 初始请求（true/false）
     */
    private String initialRequest;
    
    /**
     * 数据源类型（STATIC/DICT/API）
     */
    private String dataSourceType;
    
    /**
     * API URL（当数据源类型为API时）
     */
    private String apiUrl;
    
    /**
     * HTTP方法（GET/POST等，当数据源类型为API时）
     */
    private String httpMethod;
    
    /**
     * API参数（当数据源类型为API时）
     */
    private String apiParams;
    
    /**
     * 值字段映射（当数据源类型为API时）
     */
    private String valueKey;
    
    /**
     * 标签字段映射（当数据源类型为API时）
     */
    private String labelKey;
    
    /**
     * 转换为TypeEnum
     */
    public TypeEnum getTypeEnum() {
        if (type == null) return TypeEnum.string;
        return switch (type.toLowerCase()) {
            case "string" -> TypeEnum.string;
            case "number" -> TypeEnum.number;
            case "enum" -> TypeEnum.enums;
            case "datetime" -> TypeEnum.datetime;
            case "bool" -> TypeEnum.bool;
            case "obj" -> TypeEnum.obj;
            default -> TypeEnum.string;
        };
    }
    
    /**
     * 转换为DataSourceType
     */
    public DataSourceType getDataSourceTypeEnum() {
        if (dataSourceType == null) return DataSourceType.STATIC;
        return switch (dataSourceType.toUpperCase()) {
            case "STATIC" -> DataSourceType.STATIC;
            case "DICT" -> DataSourceType.DICT;
            case "API" -> DataSourceType.API;
            default -> DataSourceType.STATIC;
        };
    }
    
    /**
     * 转换为HttpMethod
     */
    public HttpMethod getHttpMethodEnum() {
        if (httpMethod == null) return HttpMethod.GET;
        return switch (httpMethod.toUpperCase()) {
            case "GET" -> HttpMethod.GET;
            case "POST" -> HttpMethod.POST;
            case "PUT" -> HttpMethod.PUT;
            case "DELETE" -> HttpMethod.DELETE;
            default -> HttpMethod.GET;
        };
    }
    
    /**
     * 转换为boolean
     */
    public boolean getBooleanValue(String value) {
        if (value == null) return false;
        return "true".equalsIgnoreCase(value.trim()) || "1".equals(value.trim());
    }
}
