package com.bzlj.craft.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Excel模板工具类
 * <AUTHOR>
 * @description: 用于生成Excel导入模板
 * @date 2025-07-25
 */
public class ExcelTemplateUtil {
    
    /**
     * 生成FormAttr导入模板
     * @return Excel文件字节数组
     * @throws IOException IO异常
     */
    public static byte[] generateFormAttrTemplate() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("FormAttr导入模板");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "组件ID*", "属性名称*", "属性标签*", "字段类型*", "是否支持搜索", 
                "属性排序", "是否展示", "是否范围", "属性字典项", "属性格式",
                "属性别名", "是否支持多选", "初始请求", "数据源类型", "API URL",
                "HTTP方法", "API参数", "值字段映射", "标签字段映射"
            };
            
            // 设置标题样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 4000); // 设置列宽
            }
            
            // 创建说明行
            Row descRow = sheet.createRow(1);
            String[] descriptions = {
                "必填，组件的唯一标识", "必填，属性的数据索引", "必填，显示标签", "必填，string/number/enum/datetime/bool/obj", "true/false",
                "数字，排序顺序", "true/false", "true/false", "字典编码", "日期格式等",
                "属性别名", "true/false", "true/false", "STATIC/DICT/API", "API接口地址",
                "GET/POST/PUT/DELETE", "API参数", "API返回值字段", "API返回标签字段"
            };
            
            CellStyle descStyle = workbook.createCellStyle();
            Font descFont = workbook.createFont();
            descFont.setColor(IndexedColors.GREY_50_PERCENT.getIndex());
            descFont.setItalic(true);
            descStyle.setFont(descFont);
            
            for (int i = 0; i < descriptions.length; i++) {
                Cell cell = descRow.createCell(i);
                cell.setCellValue(descriptions[i]);
                cell.setCellStyle(descStyle);
            }
            
            // 创建示例数据行
            Row exampleRow = sheet.createRow(2);
            String[] examples = {
                "COMP001", "material.name", "物料名称", "string", "true",
                "1", "true", "false", "", "",
                "", "false", "false", "STATIC", "",
                "", "", "", ""
            };
            
            for (int i = 0; i < examples.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(examples[i]);
            }
            
            // 创建第二个示例（枚举类型）
            Row example2Row = sheet.createRow(3);
            String[] examples2 = {
                "COMP001", "status", "状态", "enum", "true",
                "2", "true", "false", "STATUS_DICT", "",
                "", "false", "false", "DICT", "",
                "", "", "", ""
            };
            
            for (int i = 0; i < examples2.length; i++) {
                Cell cell = example2Row.createCell(i);
                cell.setCellValue(examples2[i]);
            }
            
            // 创建第三个示例（API数据源）
            Row example3Row = sheet.createRow(4);
            String[] examples3 = {
                "COMP001", "department", "部门", "enum", "false",
                "3", "true", "false", "", "",
                "", "false", "true", "API", "/api/departments",
                "GET", "", "id", "name"
            };
            
            for (int i = 0; i < examples3.length; i++) {
                Cell cell = example3Row.createCell(i);
                cell.setCellValue(examples3[i]);
            }
            
            // 冻结前两行
            sheet.createFreezePane(0, 2);
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }
}
