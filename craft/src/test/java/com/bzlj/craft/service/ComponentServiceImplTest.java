package com.bzlj.craft.service;

import com.bzlj.base.enums.DataSourceType;
import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.dto.ComponentDTO;
import com.bzlj.craft.enums.TypeEnum;
import com.bzlj.craft.mongo.entity.FormAttr;
import com.bzlj.craft.mongo.repository.FormAttrRepository;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-26 16:48
 */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ComponentServiceImplTest extends BaseTestNGTest {


    @Autowired
    private IComponentService componentService;

    @Autowired
    private FormAttrRepository formAttrRepository;

    @Test
    void getRepository() {
        componentService.getRepository();
    }

    @Test
    void getEntityManager() {
        componentService.getEntityManager();
    }

    @Test
    void getDTOClass() {
        componentService.getDTOClass();
    }

    @Test
    void getPOClass() {
        componentService.getPOClass();
    }

    @Test
    void findComponentByKeys(){
        componentService.getPOClass();
        componentService.getEntityManager();
        componentService.findComponentByKeys(Lists.newArrayList("TaskInfo","WorkStepList","ResultParams",
                "ProductionProcess","OperationLog","AbnormalRecord","TeamInfo",
                "QualityInspection","inputAndOutput","LoadMaterial","ProductionInputMaterial","ProductionOutMaterial"),null);
    }

    @Test
    void generateFormAttr(){
        FormAttr formAttr = new FormAttr();
        formAttr.setComponentId("19");
        formAttr.setDataIndex("material.materialId");
        formAttr.setDataSourceType(DataSourceType.STATIC);
        formAttr.setDisplay(false);
        formAttr.setInitialRequest(false);
        formAttr.setLabel("物料ID");
        formAttr.setOrder(0);
        formAttr.setSearchable(false);
        formAttr.setType(TypeEnum.string);
        formAttr.setMultiple(false);
        formAttr.setRange(false);
        formAttrRepository.save(formAttr);
    }

    @Test
    void findComponentByInputMaterial(){
        componentService.getPOClass();
        componentService.getEntityManager();
        List<ComponentDTO> componentByKeys = componentService.findComponentByKeys(Lists.newArrayList("ProductionInputMaterial"), "TB");
        System.out.println(componentByKeys);

    }
}