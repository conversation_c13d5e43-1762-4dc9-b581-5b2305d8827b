package com.bzlj.craft.service;

import com.bzlj.craft.dto.ImportResultDTO;
import com.bzlj.craft.mongo.entity.FormAttr;
import com.bzlj.craft.mongo.repository.FormAttrRepository;
import com.bzlj.craft.service.impl.FormAttrImportServiceImpl;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * FormAttr导入服务测试
 * <AUTHOR>
 * @description: FormAttr导入服务测试类
 * @date 2025-07-25
 */
class FormAttrImportServiceTest {
    
    @Mock
    private FormAttrRepository formAttrRepository;
    
    @InjectMocks
    private FormAttrImportServiceImpl formAttrImportService;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    void testGenerateTemplate() throws IOException {
        byte[] template = formAttrImportService.generateTemplate();
        assertNotNull(template);
        assertTrue(template.length > 0);
    }
    
    @Test
    void testImportFormAttr_Success() throws IOException {
        // 准备测试数据
        MockMultipartFile file = createTestExcelFile();
        
        // Mock repository行为
        when(formAttrRepository.findByComponentIdOrderByOrder(anyString())).thenReturn(new ArrayList<>());
        when(formAttrRepository.save(any(FormAttr.class))).thenReturn(new FormAttr());
        
        // 执行导入
        ImportResultDTO result = formAttrImportService.importFormAttr(file);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalCount());
        assertEquals(2, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertEquals(2, result.getInsertCount());
        assertEquals(0, result.getUpdateCount());
        assertTrue(result.isAllSuccess());
        assertTrue(result.getErrors().isEmpty());
    }
    
    @Test
    void testImportFormAttr_WithValidationErrors() throws IOException {
        // 准备包含错误数据的测试文件
        MockMultipartFile file = createTestExcelFileWithErrors();
        
        // 执行导入
        ImportResultDTO result = formAttrImportService.importFormAttr(file);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalCount());
        assertEquals(0, result.getSuccessCount());
        assertEquals(1, result.getFailureCount());
        assertFalse(result.isAllSuccess());
        assertFalse(result.getErrors().isEmpty());
    }
    
    /**
     * 创建测试用的Excel文件
     */
    private MockMultipartFile createTestExcelFile() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("FormAttr导入模板");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "组件ID", "属性名称", "属性标签", "字段类型", "是否支持搜索", 
                "属性排序", "是否展示", "是否范围", "属性字典项", "属性格式",
                "属性别名", "是否支持多选", "初始请求", "数据源类型", "API URL",
                "HTTP方法", "API参数", "值字段映射", "标签字段映射"
            };
            
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
            }
            
            // 创建说明行
            Row descRow = sheet.createRow(1);
            for (int i = 0; i < headers.length; i++) {
                descRow.createCell(i).setCellValue("说明");
            }
            
            // 创建测试数据行
            Row dataRow1 = sheet.createRow(2);
            dataRow1.createCell(0).setCellValue("COMP001");
            dataRow1.createCell(1).setCellValue("material.name");
            dataRow1.createCell(2).setCellValue("物料名称");
            dataRow1.createCell(3).setCellValue("string");
            dataRow1.createCell(4).setCellValue("true");
            dataRow1.createCell(5).setCellValue("1");
            dataRow1.createCell(6).setCellValue("true");
            dataRow1.createCell(7).setCellValue("false");
            
            Row dataRow2 = sheet.createRow(3);
            dataRow2.createCell(0).setCellValue("COMP001");
            dataRow2.createCell(1).setCellValue("status");
            dataRow2.createCell(2).setCellValue("状态");
            dataRow2.createCell(3).setCellValue("enum");
            dataRow2.createCell(4).setCellValue("true");
            dataRow2.createCell(5).setCellValue("2");
            dataRow2.createCell(6).setCellValue("true");
            dataRow2.createCell(7).setCellValue("false");
            dataRow2.createCell(8).setCellValue("STATUS_DICT");
            dataRow2.createCell(13).setCellValue("DICT");
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            
            return new MockMultipartFile(
                "file", 
                "test.xlsx", 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                new ByteArrayInputStream(outputStream.toByteArray())
            );
        }
    }
    
    /**
     * 创建包含错误数据的测试Excel文件
     */
    private MockMultipartFile createTestExcelFileWithErrors() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("FormAttr导入模板");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "组件ID", "属性名称", "属性标签", "字段类型", "是否支持搜索", 
                "属性排序", "是否展示", "是否范围", "属性字典项", "属性格式",
                "属性别名", "是否支持多选", "初始请求", "数据源类型", "API URL",
                "HTTP方法", "API参数", "值字段映射", "标签字段映射"
            };
            
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
            }
            
            // 创建说明行
            Row descRow = sheet.createRow(1);
            for (int i = 0; i < headers.length; i++) {
                descRow.createCell(i).setCellValue("说明");
            }
            
            // 创建错误数据行（缺少必填字段）
            Row dataRow = sheet.createRow(2);
            dataRow.createCell(0).setCellValue(""); // 组件ID为空
            dataRow.createCell(1).setCellValue(""); // 属性名称为空
            dataRow.createCell(2).setCellValue(""); // 属性标签为空
            dataRow.createCell(3).setCellValue("invalid_type"); // 无效的字段类型
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            
            return new MockMultipartFile(
                "file", 
                "test_error.xlsx", 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                new ByteArrayInputStream(outputStream.toByteArray())
            );
        }
    }
}
