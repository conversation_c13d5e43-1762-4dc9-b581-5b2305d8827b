package com.bzlj.craft.service;


import com.bzlj.base.querydsl.builder.QuerydslPredicateBuilder;
import com.bzlj.base.querydsl.fetch.QuerydslJoinFetcher;
import com.bzlj.base.result.DataResult;
import com.bzlj.base.result.PageResult;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.base.search.SortItem;
import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.component.command.ComponentSearch;
import com.bzlj.craft.dto.ProductionTaskDTO;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.util.JsonUtils;
import com.google.common.collect.Lists;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-12 10:59
 */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SurveillanceServiceImplTest extends BaseTestNGTest {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ISurveillanceService surveillanceService;
    @Autowired
    JPAQueryFactory queryFactory;



    @Test
    void getSurveillanceRepository(){
        surveillanceService.getRepository();
    }
    @Test
    void queryTaskByCondition(){
        SearchCondition searchCondition = new SearchCondition();
        SearchItems searchItems = SearchItems.builder()
                .item(new SearchItem("process.processType.itemCode", "1", null, SearchItem.Operator.EQ))
                .build();
        searchCondition.setSearchItems(searchItems);
        List<String> openProps = Lists.newArrayList("process.processType","process.craft","taskEquipments.equip","taskMaterials.material");
        searchCondition.setOpenProps(openProps);
        String rootAlias = "entity";
        JPAQuery<ProductionTask> query = new JPAQuery<>(entityManager);
        PathBuilder<ProductionTask> rootPath = new PathBuilder<>(ProductionTask.class, rootAlias);
        // 1. 初始化 FROM 子句
        query.from(rootPath).select(rootPath);
        // 2. 自动处理嵌套关联加载
        new QuerydslJoinFetcher<>(query, ProductionTask.class,rootAlias).fetchJoins(openProps);
        // 1. 动态构建查询条件
        QuerydslPredicateBuilder<ProductionTask> predicateBuilder =
                new QuerydslPredicateBuilder<>(ProductionTask.class, searchCondition,rootAlias);
        Predicate predicate = predicateBuilder.build();
        // 3. 执行查询
        List<ProductionTask> fetch = query.where(predicate).fetch();
    }

    @Test
    void findByCondition(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setPageSize(10);
        searchCondition.setPageCurrent(1);
        SortItem sortItem = SortItem.builder().fieldName("process.processType.itemCode").sortOrder(SortItem.DESC_ORDER_NAME).build();
        searchCondition.setSortItems(Lists.newArrayList(sortItem));
        PageResult<ProductionTaskDTO> result = surveillanceService.findByCondition(searchCondition);
    }

    @Test
    void findByCondition2(){
        SearchCondition searchCondition = new SearchCondition();
        SearchItems searchItems = SearchItems.builder()
                .item(new SearchItem("process.processType.itemCode", "1", null, SearchItem.Operator.EQ))
                .build();
        searchCondition.setSearchItems(searchItems);
        List<String> openProps = Lists.newArrayList("process.processType","process.craft","taskEquipments.equip","taskMaterials.material");
        searchCondition.setOpenProps(openProps);
        searchCondition.setPageSize(10);
        searchCondition.setPageCurrent(1);
        SortItem sortItem = SortItem.builder().fieldName("process.processType.itemCode").sortOrder(SortItem.DESC_ORDER_NAME).build();
        searchCondition.setSortItems(Lists.newArrayList(sortItem));
        surveillanceService.getSurveillanceList(JsonUtils.toJson(searchCondition));
    }

    @Test
    void findByCondition3(){
        SearchCondition searchCondition = new SearchCondition();
        SearchItems searchItems = SearchItems.builder()
                .item(new SearchItem("taskId", "TASK326001", null, SearchItem.Operator.EQ))
                .build();
        searchCondition.setSearchItems(searchItems);
        List<String> openProps = Lists.newArrayList("process.processType","taskEquipments","taskMaterials");
        searchCondition.setOpenProps(openProps);
        searchCondition.setPageSize(10);
        searchCondition.setPageCurrent(1);
        SortItem sortItem = SortItem.builder().fieldName("process.processType.itemCode").sortOrder(SortItem.DESC_ORDER_NAME).build();
        searchCondition.setSortItems(Lists.newArrayList(sortItem));
        surveillanceService.getSurveillanceList(JsonUtils.toJson(searchCondition));
    }


    @Test
    void getTaskStatusStatistics(){
        DataResult taskStatusStatistics = surveillanceService.getTaskStatusStatistics("锻造");
    }


    @Test
    void findSurveillanceStep(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskId", "TASK001", null, SearchItem.Operator.EQ))
                .build());

        searchCondition.setOpenProps(Lists.newArrayList("process","taskMaterials","process.craft","taskEquipments","taskEquipments.equip"));
        DataResult surveillanceStep = surveillanceService.findSurveillanceStep(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Test
    void findWorkStepByTask(){
        surveillanceService.findWorkStepByTask("","");
    }

    @Test
    void findStepResultParams(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskId", "TASK_VAR_17", null, SearchItem.Operator.EQ))
                .build());

        searchCondition.setOpenProps(Lists.newArrayList("process"));
        DataResult surveillanceStep = surveillanceService.findStepResultParams(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Test
    void findOperation(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "TASK001", null, SearchItem.Operator.EQ))
                .build());

        DataResult surveillanceStep = surveillanceService.findOperation(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Test
    void findProductionMaterials(){
        ComponentSearch componentSearch = new ComponentSearch();
        componentSearch.setComponentId("23");
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "334239222900854784", null, SearchItem.Operator.EQ))
                .item(new SearchItem("relationType", false, null, SearchItem.Operator.EQ))
                .build());
        componentSearch.setSearchCondition(searchCondition);
        DataResult surveillanceStep = surveillanceService.findProductionMaterials(JsonUtils.toJson(componentSearch));
        System.out.println(surveillanceStep);
    }

    @Test
    void findProductionMaterialsWithComponent(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "TASK001", null, SearchItem.Operator.EQ))
                .item(new SearchItem("relationType", true, null, SearchItem.Operator.EQ))
                .build());

        ComponentSearch componentSearch = new ComponentSearch();
        componentSearch.setComponentId("19"); // 使用测试中的componentId
        componentSearch.setSearchCondition(searchCondition);

        DataResult result = surveillanceService.findProductionMaterials(JsonUtils.toJson(componentSearch));
        System.out.println("组件化查询结果: " + result);

        // 验证返回的数据格式是否为链式数据 [["1","2"],["aa","bb"]]
        if (result.getList() instanceof List) {
            List<?> list = (List<?>) result.getList();
            System.out.println("链式数据格式: " + list);
            System.out.println("数据类型: " + list.getClass().getSimpleName());
            if (!list.isEmpty()) {
                System.out.println("第一个元素类型: " + list.get(0).getClass().getSimpleName());
            }
        }
    }

    @Test
    void findLoadMaterialRecorde(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "TASK001", null, SearchItem.Operator.EQ))
                .build());

        DataResult surveillanceStep = surveillanceService.findLoadMaterialRecorde(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }


    @Test
    void findTeamInfo(){
        Map<String,String> map = new HashMap<>();
        map.put("taskId","TASK001");
        DataResult surveillanceStep = surveillanceService.findTeamInfo(JsonUtils.toJson(map));
        System.out.println(surveillanceStep);
    }

    @Test
    void findContinuousParams(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskId", "TASK326001", null, SearchItem.Operator.EQ))
                .build());

        searchCondition.setOpenProps(Lists.newArrayList("process"));
        DataResult surveillanceStep = surveillanceService.findContinuousParams(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }


    @Test
    void findContinuousParams2(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskId", "pt-002", null, SearchItem.Operator.EQ))
                .build());

        searchCondition.setOpenProps(Lists.newArrayList("process"));
        DataResult surveillanceStep = surveillanceService.findContinuousParams(JsonUtils.toJson(searchCondition));
        System.out.println(surveillanceStep);
    }

    @Test
    void findInspectionData(){
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("task.taskId", "TASK327002", null, SearchItem.Operator.EQ))
                .build());
        DataResult c = surveillanceService.findInspectionData(JsonUtils.toJson(searchCondition));
        System.out.println(c);
    }

    @Test
    void findAbnormalRecords(){
        Map<String,String> map = new HashMap<>();
        map.put("taskId","TASK001");
        DataResult c = surveillanceService.findAbnormalRecords(JsonUtils.toJson(map));
        System.out.println(c);
    }

    @Test
    void findAbnormalRecords2(){
        Map<String,String> map = new HashMap<>();
        try {
            DataResult result = surveillanceService.findAbnormalRecords(JsonUtils.toJson(map));
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }

    @Test
    void findAbnormalRecords3(){
        Map<String,String> map = new HashMap<>();
        try {
            DataResult result1 = surveillanceService.findAbnormalRecords(null);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }

    @Test
    void findAbnormalRecords4(){
        Map<String,String> map = new HashMap<>();
        map.put("taskId","TASK002");
        DataResult c = surveillanceService.findAbnormalRecords(JsonUtils.toJson(map));
        System.out.println(c);
    }

    @Test
    void fetchPointData(){
        Map<String,Object> map = new HashMap<>();
        map.put("dataCode","Model2408090014");
        map.put("start",1742524051000L);
        map.put("stop",1742570851000L);
        map.put("windowFrequency",1);
        map.put("timeUnit","Seconds");
        map.put("functionName","FIRST");
        map.put("createEmpty",false);

        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.batch_point));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.single_point));


        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.first_point_in_range_date));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.latest_point_in_range_date));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.max_point_in_range_date));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.min_point_in_range_date));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.mean_point_in_range_date));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.sum_point_in_range_date));

        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.end_subtract_start_point));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.spread_point_in_range_date));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.upper_lower_point_in_range_date));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.status_statistics_boolean));
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.end_subtract_start_point_2));

        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(map), PointMethodType.calculator_point_in_range_date));

        List<Map<String,Object>> list = new ArrayList<>();
        list.add(map);
        System.out.println(surveillanceService.fetchPointData(JsonUtils.toJson(list), PointMethodType.batch_concurrent_point));

    }

    @Test
    void findTaskStatus(){
        System.out.println(surveillanceService.findTaskStatus("TASK31901"));
        System.out.println(surveillanceService.findTaskStatus("TASK3190"));
    }
}